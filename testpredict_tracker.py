#!/usr/bin/env python3
"""
YOLO 多模式智能检测系统 (v3.0 - 重构版)

功能特性:
- 图片检测: 单张或批量图片处理
- 视频检测: 视频文件处理，支持目标追踪
- 实时流检测: RTMP, RTSP, 摄像头等视频流处理
- TensorRT加速: 支持模型导出和加速推理
- 多GPU并行: 自动检测并利用多GPU进行并行处理
- 分辨率优化: 等比缩放至指定尺寸
- 智能统计: 过滤短暂出现的检测目标
- 周期性报告: API数据上报功能
- HLS流媒体: 实时流输出支持
"""

import os
import argparse
import threading
import subprocess
import time
import socket
import json
import base64
import glob
import multiprocessing as mp
from collections import deque
from datetime import datetime
from pathlib import Path
from typing import List, Optional, Union, Tuple, Dict, Any
from concurrent.futures import ThreadPoolExecutor, as_completed

from ultralytics import YOLO
import cv2
import requests
import torch
import numpy as np
from flask import Flask, send_file, render_template_string
from flask_cors import CORS

# --- 配置常量 ---
API_ENDPOINT = "http://***************:48902/api/AnalysisResult/Push"
DEFAULT_CONF_THRESHOLD = 0.25
DEFAULT_IOU_THRESHOLD = 0.45
DEFAULT_TARGET_SIZE = 1280
DEFAULT_IMGSZ = 640
DEFAULT_STREAM_PORT = 6006

# --- 基础检测器类 ---
class BaseDetector:
    """基础检测器类，提供模型加载、TensorRT支持、多GPU支持等核心功能"""

    def __init__(self, model_path: str, device: str = '', use_tensorrt: bool = False,
                 conf_threshold: float = DEFAULT_CONF_THRESHOLD,
                 iou_threshold: float = DEFAULT_IOU_THRESHOLD,
                 imgsz: int = DEFAULT_IMGSZ):
        """
        初始化基础检测器

        Args:
            model_path: 模型文件路径
            device: 设备选择 ('', 'cpu', '0', '0,1' 等)
            use_tensorrt: 是否使用TensorRT加速
            conf_threshold: 置信度阈值
            iou_threshold: NMS IOU阈值
            imgsz: 推理图片大小
        """
        self.model_path = model_path
        self.device = device
        self.use_tensorrt = use_tensorrt
        self.conf_threshold = conf_threshold
        self.iou_threshold = iou_threshold
        self.imgsz = imgsz

        # 检测可用GPU
        self.available_gpus = self._detect_gpus()
        self.model = None
        self.tensorrt_model_path = None

        # 初始化模型
        self._initialize_model()

    def _detect_gpus(self) -> List[int]:
        """检测可用的GPU设备"""
        if not torch.cuda.is_available():
            print("⚠️ CUDA不可用，将使用CPU进行推理")
            return []

        gpu_count = torch.cuda.device_count()
        gpus = list(range(gpu_count))
        print(f"✅ 检测到 {gpu_count} 个GPU设备: {gpus}")

        for i in gpus:
            gpu_name = torch.cuda.get_device_name(i)
            gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
            print(f"   GPU {i}: {gpu_name} ({gpu_memory:.1f}GB)")

        return gpus

    def _get_tensorrt_model_path(self) -> str:
        """生成TensorRT模型路径"""
        base_path = Path(self.model_path)
        return str(base_path.with_suffix('.engine'))

    def export_to_tensorrt(self, force: bool = False) -> bool:
        """
        导出模型到TensorRT格式

        Args:
            force: 是否强制重新导出

        Returns:
            bool: 导出是否成功
        """
        self.tensorrt_model_path = self._get_tensorrt_model_path()

        if os.path.exists(self.tensorrt_model_path) and not force:
            print(f"✅ TensorRT模型已存在: {self.tensorrt_model_path}")
            return True

        print(f"🔄 正在导出TensorRT模型...")
        try:
            # 加载原始模型进行导出
            temp_model = YOLO(self.model_path)
            temp_model.export(
                format='engine',
                imgsz=self.imgsz,
                device=self.device or 0,
                half=True,  # 使用FP16精度
                dynamic=False,
                simplify=True,
                workspace=4  # 4GB workspace
            )
            print(f"✅ TensorRT模型导出成功: {self.tensorrt_model_path}")
            return True
        except Exception as e:
            print(f"❌ TensorRT导出失败: {e}")
            return False

    def _initialize_model(self):
        """初始化模型"""
        if not os.path.exists(self.model_path):
            raise FileNotFoundError(f"模型文件不存在: {self.model_path}")

        # 尝试使用TensorRT模型
        if self.use_tensorrt:
            self.tensorrt_model_path = self._get_tensorrt_model_path()
            if os.path.exists(self.tensorrt_model_path):
                print(f"🚀 加载TensorRT模型: {self.tensorrt_model_path}")
                self.model = YOLO(self.tensorrt_model_path)
            else:
                print(f"⚠️ TensorRT模型不存在，尝试导出...")
                if self.export_to_tensorrt():
                    self.model = YOLO(self.tensorrt_model_path)
                else:
                    print(f"⚠️ 回退到原始模型: {self.model_path}")
                    self.model = YOLO(self.model_path)
        else:
            print(f"🧠 加载原始模型: {self.model_path}")
            self.model = YOLO(self.model_path)

        # 设置设备
        if self.device:
            self.model.to(self.device)
            print(f"✅ 模型已加载到设备: {self.device}")
        elif self.available_gpus:
            self.model.to(0)  # 默认使用第一个GPU
            print(f"✅ 模型已加载到GPU: 0")
        else:
            print("✅ 模型已加载到CPU")

    def get_multi_gpu_devices(self) -> List[str]:
        """获取多GPU设备列表"""
        if len(self.available_gpus) <= 1:
            return [self.device or '0' if self.available_gpus else 'cpu']

        return [str(gpu) for gpu in self.available_gpus]

    def benchmark_performance(self, test_image_path: str = None, iterations: int = 100) -> Dict[str, float]:
        """
        性能基准测试

        Args:
            test_image_path: 测试图片路径
            iterations: 测试迭代次数

        Returns:
            Dict: 性能统计信息
        """
        if test_image_path and not os.path.exists(test_image_path):
            print(f"⚠️ 测试图片不存在: {test_image_path}")
            return {}

        # 创建测试图片
        if not test_image_path:
            test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
        else:
            test_image = cv2.imread(test_image_path)

        print(f"🔄 开始性能测试 ({iterations} 次迭代)...")

        # 预热
        for _ in range(10):
            _ = self.model(test_image, conf=self.conf_threshold, iou=self.iou_threshold, verbose=False)

        # 正式测试
        start_time = time.time()
        for _ in range(iterations):
            _ = self.model(test_image, conf=self.conf_threshold, iou=self.iou_threshold, verbose=False)
        end_time = time.time()

        total_time = end_time - start_time
        avg_time = total_time / iterations
        fps = 1.0 / avg_time

        results = {
            'total_time': total_time,
            'avg_inference_time': avg_time,
            'fps': fps,
            'iterations': iterations
        }

        print(f"📊 性能测试结果:")
        print(f"   平均推理时间: {avg_time*1000:.2f}ms")
        print(f"   理论FPS: {fps:.2f}")

        return results

# --- 工具函数 ---
def get_local_ip():
    """获取本机局域网IP地址"""
    import socket
    s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    try:
        s.connect(('**************', 1))
        IP = s.getsockname()[0]
    except Exception:
        IP = '127.0.0.1'
    finally:
        s.close()
    return IP

def draw_boxes_only(image, boxes, thickness=2):
    """仅在图像上绘制边界框。"""
    if boxes is None:
        return image
    annotated_img = image.copy()
    for box in boxes:
        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy().astype(int)
        cv2.rectangle(annotated_img, (x1, y1), (x2, y2), (0, 255, 255), thickness)
    return annotated_img

def calculate_scaled_resolution(original_width, original_height, target_size=1280):
    """计算按比例缩放后的分辨率，确保长边为目标尺寸且宽高为偶数。"""
    if original_width == 0 or original_height == 0:
        return target_size, int(target_size / (16/9))

    if original_width <= target_size and original_height <= target_size:
        new_width = original_width if original_width % 2 == 0 else original_width - 1
        new_height = original_height if original_height % 2 == 0 else original_height - 1
        return new_width, new_height

    if original_width > original_height:
        aspect_ratio = original_height / original_width
        new_width = target_size
        new_height = int(new_width * aspect_ratio)
    else:
        aspect_ratio = original_width / original_height
        new_height = target_size
        new_width = int(new_height * aspect_ratio)

    new_width = new_width if new_width % 2 == 0 else new_width - 1
    new_height = new_height if new_height % 2 == 0 else new_height - 1
    
    return new_width, new_height

# --- 图片检测器类 ---
class ImageDetector(BaseDetector):
    """图片检测器类，支持单张或批量图片检测"""

    def __init__(self, model_path: str, output_dir: str = "output_images", **kwargs):
        """
        初始化图片检测器

        Args:
            model_path: 模型文件路径
            output_dir: 输出目录
            **kwargs: 其他参数传递给BaseDetector
        """
        super().__init__(model_path, **kwargs)
        self.output_dir = output_dir
        os.makedirs(self.output_dir, exist_ok=True)

    def detect_single_image(self, image_path: str, save_result: bool = True,
                          show_result: bool = False) -> Dict[str, Any]:
        """
        检测单张图片

        Args:
            image_path: 图片路径
            save_result: 是否保存结果
            show_result: 是否显示结果

        Returns:
            Dict: 检测结果
        """
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"图片文件不存在: {image_path}")

        print(f"🔍 正在检测图片: {image_path}")

        # 读取图片
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图片: {image_path}")

        # 执行检测
        start_time = time.time()
        results = self.model(image, conf=self.conf_threshold, iou=self.iou_threshold,
                           imgsz=self.imgsz, verbose=False)
        inference_time = time.time() - start_time

        # 处理结果
        result_data = self._process_detection_results(results[0], image, image_path,
                                                    inference_time, save_result, show_result)

        return result_data

    def detect_batch_images(self, image_paths: List[str], max_workers: int = None) -> List[Dict[str, Any]]:
        """
        批量检测图片

        Args:
            image_paths: 图片路径列表
            max_workers: 最大并行工作线程数

        Returns:
            List[Dict]: 检测结果列表
        """
        if not image_paths:
            return []

        # 设置默认并行数
        if max_workers is None:
            max_workers = min(len(self.available_gpus) or 1, len(image_paths), 4)

        print(f"🔍 开始批量检测 {len(image_paths)} 张图片 (并行数: {max_workers})")

        results = []
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_path = {
                executor.submit(self.detect_single_image, path, True, False): path
                for path in image_paths
            }

            for future in as_completed(future_to_path):
                path = future_to_path[future]
                try:
                    result = future.result()
                    results.append(result)
                    print(f"✅ 完成: {path}")
                except Exception as e:
                    print(f"❌ 错误 {path}: {e}")
                    results.append({"image_path": path, "error": str(e)})

        print(f"✅ 批量检测完成，成功处理 {len([r for r in results if 'error' not in r])} 张图片")
        return results

    def detect_directory(self, directory_path: str, extensions: List[str] = None) -> List[Dict[str, Any]]:
        """
        检测目录中的所有图片

        Args:
            directory_path: 目录路径
            extensions: 支持的图片扩展名

        Returns:
            List[Dict]: 检测结果列表
        """
        if extensions is None:
            extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']

        if not os.path.exists(directory_path):
            raise FileNotFoundError(f"目录不存在: {directory_path}")

        # 查找所有图片文件
        image_paths = []
        for ext in extensions:
            pattern = os.path.join(directory_path, f"*{ext}")
            image_paths.extend(glob.glob(pattern))
            pattern = os.path.join(directory_path, f"*{ext.upper()}")
            image_paths.extend(glob.glob(pattern))

        if not image_paths:
            print(f"⚠️ 目录中未找到图片文件: {directory_path}")
            return []

        print(f"📁 在目录 {directory_path} 中找到 {len(image_paths)} 张图片")
        return self.detect_batch_images(image_paths)

    def _process_detection_results(self, result, image, image_path: str, inference_time: float,
                                 save_result: bool, show_result: bool) -> Dict[str, Any]:
        """处理检测结果"""
        # 提取检测信息
        detections = []
        if result.boxes is not None:
            boxes = result.boxes.xyxy.cpu().numpy()
            confidences = result.boxes.conf.cpu().numpy()
            classes = result.boxes.cls.cpu().numpy()

            for i, (box, conf, cls) in enumerate(zip(boxes, confidences, classes)):
                detections.append({
                    'bbox': box.tolist(),
                    'confidence': float(conf),
                    'class': int(cls),
                    'class_name': self.model.names[int(cls)]
                })

        # 绘制结果
        annotated_image = draw_boxes_only(image, result.boxes)

        # 保存结果
        output_path = None
        if save_result:
            filename = os.path.basename(image_path)
            name, ext = os.path.splitext(filename)
            output_filename = f"{name}_detected{ext}"
            output_path = os.path.join(self.output_dir, output_filename)
            cv2.imwrite(output_path, annotated_image)

        # 显示结果
        if show_result:
            cv2.imshow(f"Detection Result - {os.path.basename(image_path)}", annotated_image)
            cv2.waitKey(0)
            cv2.destroyAllWindows()

        return {
            'image_path': image_path,
            'output_path': output_path,
            'detections': detections,
            'detection_count': len(detections),
            'inference_time': inference_time,
            'image_size': image.shape[:2]
        }

# --- 视频检测器类 ---
class VideoDetector(BaseDetector):
    """视频检测器类，支持视频文件检测和追踪功能"""

    def __init__(self, model_path: str, output_dir: str = "output_videos", **kwargs):
        """
        初始化视频检测器

        Args:
            model_path: 模型文件路径
            output_dir: 输出目录
            **kwargs: 其他参数传递给BaseDetector
        """
        super().__init__(model_path, **kwargs)
        self.output_dir = output_dir
        os.makedirs(self.output_dir, exist_ok=True)

    def detect_video(self, video_path: str, save_result: bool = True,
                    show_result: bool = False, target_size: int = DEFAULT_TARGET_SIZE,
                    enable_tracking: bool = True, max_det: int = 300) -> Dict[str, Any]:
        """
        检测视频文件

        Args:
            video_path: 视频文件路径
            save_result: 是否保存结果视频
            show_result: 是否实时显示结果
            target_size: 目标分辨率
            enable_tracking: 是否启用目标追踪
            max_det: 最大检测数量

        Returns:
            Dict: 检测结果统计
        """
        if not os.path.exists(video_path):
            raise FileNotFoundError(f"视频文件不存在: {video_path}")

        print(f"🎬 正在处理视频: {video_path}")

        # 打开视频
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise ValueError(f"无法打开视频文件: {video_path}")

        # 获取视频信息
        fps = cap.get(cv2.CAP_PROP_FPS) or 25
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        original_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        original_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

        print(f"📹 视频信息: {original_width}x{original_height}, {fps:.2f}FPS, {total_frames}帧")

        # 计算输出分辨率
        scaled_width, scaled_height = calculate_scaled_resolution(
            original_width, original_height, target_size
        )
        print(f"✅ 输出分辨率: {scaled_width}x{scaled_height}")

        # 设置输出视频
        output_path = None
        out = None
        if save_result:
            filename = os.path.basename(video_path)
            name, ext = os.path.splitext(filename)
            output_filename = f"{name}_detected{ext}"
            output_path = os.path.join(self.output_dir, output_filename)

            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, (scaled_width, scaled_height))

        # 追踪相关变量
        min_consecutive_frames = int(fps)  # 1秒的帧数
        track_counters = {}  # {track_id: frame_count}

        # 统计变量
        frame_count = 0
        total_detections = 0
        stable_detections = 0
        max_objects_in_frame = 0
        processing_times = []

        try:
            while True:
                ret, frame = cap.read()
                if not ret:
                    break

                frame_count += 1

                # 调整帧大小
                resized_frame = cv2.resize(frame, (scaled_width, scaled_height))

                # 执行检测/追踪
                start_time = time.time()
                if enable_tracking:
                    results = self.model.track(
                        resized_frame, persist=True, conf=self.conf_threshold,
                        iou=self.iou_threshold, max_det=max_det, imgsz=self.imgsz, verbose=False
                    )
                else:
                    results = self.model(
                        resized_frame, conf=self.conf_threshold,
                        iou=self.iou_threshold, imgsz=self.imgsz, verbose=False
                    )
                processing_time = time.time() - start_time
                processing_times.append(processing_time)

                # 处理追踪结果
                current_stable_count = 0
                current_track_ids = set()

                if results[0].boxes is not None:
                    total_detections += len(results[0].boxes)
                    max_objects_in_frame = max(max_objects_in_frame, len(results[0].boxes))

                    if enable_tracking and results[0].boxes.id is not None:
                        # 更新追踪计数器
                        current_track_ids = set(results[0].boxes.id.cpu().numpy().astype(int))

                        for track_id in current_track_ids:
                            track_counters[track_id] = track_counters.get(track_id, 0) + 1

                        # 清理消失的目标
                        disappeared_ids = set(track_counters.keys()) - current_track_ids
                        for track_id in disappeared_ids:
                            del track_counters[track_id]

                        # 计算稳定目标数量
                        current_stable_count = sum(
                            1 for count in track_counters.values()
                            if count >= min_consecutive_frames
                        )
                        stable_detections = max(stable_detections, current_stable_count)

                # 绘制检测框
                annotated_frame = draw_boxes_only(resized_frame, results[0].boxes)

                # 添加信息文本
                info_text = f"Frame: {frame_count}/{total_frames}"
                if enable_tracking:
                    info_text += f" | Stable: {current_stable_count} | Total: {len(current_track_ids)}"
                else:
                    info_text += f" | Detected: {len(results[0].boxes) if results[0].boxes is not None else 0}"

                cv2.putText(annotated_frame, info_text, (10, 30),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

                # 保存帧
                if out is not None:
                    out.write(annotated_frame)

                # 显示结果
                if show_result:
                    cv2.imshow('Video Detection', annotated_frame)
                    if cv2.waitKey(1) & 0xFF == ord('q'):
                        break

                # 进度显示
                if frame_count % 100 == 0:
                    progress = (frame_count / total_frames) * 100
                    avg_time = np.mean(processing_times[-100:])
                    print(f"进度: {progress:.1f}% ({frame_count}/{total_frames}), "
                          f"平均处理时间: {avg_time*1000:.2f}ms")

        finally:
            cap.release()
            if out is not None:
                out.release()
            if show_result:
                cv2.destroyAllWindows()

        # 计算统计信息
        avg_processing_time = np.mean(processing_times) if processing_times else 0
        total_time = sum(processing_times)

        result_data = {
            'video_path': video_path,
            'output_path': output_path,
            'total_frames': frame_count,
            'total_detections': total_detections,
            'stable_detections': stable_detections,
            'max_objects_in_frame': max_objects_in_frame,
            'avg_processing_time': avg_processing_time,
            'total_processing_time': total_time,
            'fps': fps,
            'resolution': (scaled_width, scaled_height),
            'tracking_enabled': enable_tracking
        }

        print(f"✅ 视频处理完成:")
        print(f"   总帧数: {frame_count}")
        print(f"   总检测数: {total_detections}")
        if enable_tracking:
            print(f"   最大稳定目标数: {stable_detections}")
        print(f"   平均处理时间: {avg_processing_time*1000:.2f}ms/帧")
        print(f"   总处理时间: {total_time:.2f}秒")
        if output_path:
            print(f"   输出文件: {output_path}")

        return result_data

# --- 实时流检测器类 ---
class StreamDetector(BaseDetector):
    """实时流检测器类，支持RTMP、RTSP、摄像头等视频流处理"""

    def __init__(self, model_path: str, stream_port: int = DEFAULT_STREAM_PORT, **kwargs):
        """
        初始化实时流检测器

        Args:
            model_path: 模型文件路径
            stream_port: HLS流媒体服务端口
            **kwargs: 其他参数传递给BaseDetector
        """
        super().__init__(model_path, **kwargs)
        self.stream_port = stream_port
        self.hls_dir = "hls_output"
        os.makedirs(self.hls_dir, exist_ok=True)

        # 创建Flask应用
        self.app = Flask(__name__)
        CORS(self.app)
        self._setup_routes()

    def _setup_routes(self):
        """设置Flask路由"""
        player_html = """
        <!DOCTYPE html><html><head><title>Live Detection Stream</title><meta charset="UTF-8"><script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script><style>body, html { margin: 0; padding: 0; height: 100%; background-color: #000; overflow: hidden; } video { width: 100%; height: 100%; object-fit: contain; }</style></head><body><video id="video" controls autoplay muted></video><script>const video = document.getElementById('video'); const hls_url = '/live.m3u8'; if (Hls.isSupported()) { const hls = new Hls(); hls.loadSource(hls_url); hls.attachMedia(video); } else if (video.canPlayType('application/vnd.apple.mpegurl')) { video.src = hls_url; }</script></body></html>
        """

        @self.app.route('/')
        def index():
            return render_template_string(player_html)

        @self.app.route('/live.m3u8')
        def serve_m3u8():
            return send_file(os.path.join(self.hls_dir, "live.m3u8"),
                           mimetype='application/vnd.apple.mpegurl')

        @self.app.route('/live<int:segment_num>.ts')
        def serve_segment(segment_num):
            return send_file(os.path.join(self.hls_dir, f"live{segment_num:03d}.ts"),
                           mimetype='video/mp2t')

    def start_streaming(self, source: str, target_size: int = DEFAULT_TARGET_SIZE,
                       max_det: int = 300, public_url: str = "",
                       enable_api_report: bool = True, report_interval: int = 15):
        """
        启动实时流检测和HLS推流

        Args:
            source: 视频源 (RTMP/RTSP URL, 摄像头索引等)
            target_size: 目标分辨率
            max_det: 最大检测数量
            public_url: 公网访问URL
            enable_api_report: 是否启用API报告
            report_interval: 报告间隔(秒)
        """
        # 启动HLS服务器
        self._start_hls_server()

        # 打印访问信息
        self._print_access_info(public_url)

        # 开始流处理
        self._process_stream(source, target_size, max_det, public_url,
                           enable_api_report, report_interval)

    def _start_hls_server(self):
        """启动HLS服务器"""
        def run_server():
            try:
                from waitress import serve
                serve(self.app, host='0.0.0.0', port=self.stream_port)
            except ImportError:
                self.app.run(host='0.0.0.0', port=self.stream_port)

        hls_thread = threading.Thread(target=run_server, daemon=True)
        hls_thread.start()
        time.sleep(2)  # 等待服务器启动

    def _print_access_info(self, public_url: str):
        """打印访问信息"""
        print("\n" + "="*60)
        print("🚀 HLS 流媒体服务已启动 🚀")

        if public_url:
            base_url = public_url.rstrip('/')
            hls_stream_url = f"{base_url}/live.m3u8"
            player_page_url = f"{base_url}/"
            print(f"✅ HLS流地址: {hls_stream_url}")
            print(f"🌐 播放页面: {player_page_url}")
        else:
            print("⚠️ 警告: 未提供公网URL")
            print(f"   本地访问: http://localhost:{self.stream_port}")

        print("="*60 + "\n")

    def _process_stream(self, source: str, target_size: int, max_det: int,
                       public_url: str, enable_api_report: bool, report_interval: int):
        """处理视频流"""
        # 打开视频源
        cap = cv2.VideoCapture(source)
        if not cap.isOpened():
            print(f"❌ 错误: 无法打开视频源 {source}")
            return

        # 获取视频信息
        original_fps = cap.get(cv2.CAP_PROP_FPS) or 25
        original_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        original_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

        print(f"ℹ️ 源信息: {original_width}x{original_height} @ {original_fps:.2f} FPS")

        # 计算输出分辨率
        scaled_width, scaled_height = calculate_scaled_resolution(
            original_width, original_height, target_size
        )
        print(f"✅ 输出流分辨率: {scaled_width}x{scaled_height}")

        # 启动FFmpeg进程
        ffmpeg_process = self._start_ffmpeg_process(scaled_width, scaled_height, original_fps)

        # 追踪和统计变量
        min_consecutive_frames = int(original_fps)
        track_counters = {}
        last_report_time = time.time()
        interval_max_objects = 0
        interval_best_frame_time = None
        snapshot_path = "snapshots/latest_snapshot.jpg"
        os.makedirs("snapshots", exist_ok=True)

        try:
            while True:
                ret, frame = cap.read()
                if not ret:
                    print("⚠️ 视频流中断，尝试重连...")
                    cap.release()
                    time.sleep(2)
                    cap = cv2.VideoCapture(source)
                    if not cap.isOpened():
                        print("❌ 重连失败，退出")
                        break
                    continue

                # 调整帧大小
                resized_frame = cv2.resize(frame, (scaled_width, scaled_height))

                # 执行追踪检测
                results = self.model.track(
                    resized_frame, persist=True, conf=self.conf_threshold,
                    iou=self.iou_threshold, max_det=max_det, imgsz=self.imgsz, verbose=False
                )

                # 处理追踪结果
                stable_count = 0
                current_track_ids = set()

                if results[0].boxes is not None and results[0].boxes.id is not None:
                    current_track_ids = set(results[0].boxes.id.cpu().numpy().astype(int))

                    # 更新计数器
                    for track_id in current_track_ids:
                        track_counters[track_id] = track_counters.get(track_id, 0) + 1

                    # 清理消失的目标
                    disappeared_ids = set(track_counters.keys()) - current_track_ids
                    for track_id in disappeared_ids:
                        del track_counters[track_id]

                    # 计算稳定目标数量
                    stable_count = sum(
                        1 for count in track_counters.values()
                        if count >= min_consecutive_frames
                    )

                # 更新最佳帧
                if stable_count > interval_max_objects:
                    interval_max_objects = stable_count
                    interval_best_frame_time = datetime.now()
                    annotated_best_frame = draw_boxes_only(resized_frame, results[0].boxes)
                    cv2.imwrite(snapshot_path, annotated_best_frame)

                # 绘制当前帧
                annotated_frame = draw_boxes_only(resized_frame, results[0].boxes)

                # 写入FFmpeg
                try:
                    ffmpeg_process.stdin.write(annotated_frame.tobytes())
                except (BrokenPipeError, OSError):
                    print("❌ FFmpeg 进程关闭，停止推流")
                    break

                # API报告
                if enable_api_report:
                    current_time = time.time()
                    if current_time - last_report_time >= report_interval:
                        self._send_api_report(interval_max_objects, interval_best_frame_time,
                                            public_url, snapshot_path)

                        # 重置统计
                        last_report_time = current_time
                        interval_max_objects = 0
                        interval_best_frame_time = None

                        # 清理快照
                        if os.path.exists(snapshot_path):
                            try:
                                os.remove(snapshot_path)
                            except OSError:
                                pass

        except KeyboardInterrupt:
            print("\nℹ️ 收到中断信号，正在停止服务...")
        finally:
            cap.release()
            if ffmpeg_process and ffmpeg_process.stdin:
                try:
                    ffmpeg_process.stdin.close()
                except (BrokenPipeError, OSError):
                    pass
            if ffmpeg_process:
                ffmpeg_process.wait()
            print("✅ 流处理已安全停止")

    def _start_ffmpeg_process(self, width: int, height: int, fps: float):
        """启动FFmpeg进程"""
        ffmpeg_cmd = [
            'ffmpeg', '-loglevel', 'error', '-y', '-f', 'rawvideo', '-vcodec', 'rawvideo',
            '-pix_fmt', 'bgr24', '-s', f'{width}x{height}', '-r', str(fps), '-i', '-',
            '-c:v', 'libx264', '-preset', 'ultrafast', '-tune', 'zerolatency',
            '-g', str(int(fps * 2)), '-sc_threshold', '0', '-f', 'hls',
            '-hls_time', '2', '-hls_list_size', '5',
            '-hls_flags', 'delete_segments+program_date_time',
            '-hls_segment_filename', os.path.join(self.hls_dir, 'live%03d.ts'),
            os.path.join(self.hls_dir, 'live.m3u8'),
        ]
        return subprocess.Popen(ffmpeg_cmd, stdin=subprocess.PIPE)

    def _send_api_report(self, max_objects: int, best_time: datetime,
                        public_url: str, snapshot_path: str):
        """发送API报告"""
        base64_picture = None
        if os.path.exists(snapshot_path):
            try:
                with open(snapshot_path, "rb") as image_file:
                    base64_picture = base64.b64encode(image_file.read()).decode('utf-8')
            except Exception as e:
                print(f"❌ 图片读取失败: {e}")

        hls_stream_url = f"{public_url.rstrip('/')}/live.m3u8" if public_url else ""

        report_data = {
            "pig_amount": max_objects,
            "detect_time": best_time.strftime('%Y-%m-%d %H:%M:%S') if best_time else datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "url": hls_stream_url,
            "picture": base64_picture
        }

        print(f"\n--- API报告 ---")
        print(f"检测数量: {report_data['pig_amount']}")
        print(f"检测时间: {report_data['detect_time']}")
        print("---------------\n")

        send_report_to_api(report_data)

# --- 多GPU并行处理类 ---
class MultiGPUDetector:
    """多GPU并行检测器"""

    def __init__(self, model_path: str, gpu_devices: List[str], **kwargs):
        """
        初始化多GPU检测器

        Args:
            model_path: 模型路径
            gpu_devices: GPU设备列表
            **kwargs: 其他参数
        """
        self.model_path = model_path
        self.gpu_devices = gpu_devices
        self.kwargs = kwargs
        self.detectors = []

        # 为每个GPU创建检测器
        for device in gpu_devices:
            detector = BaseDetector(model_path, device=device, **kwargs)
            self.detectors.append(detector)

        print(f"✅ 初始化了 {len(self.detectors)} 个GPU检测器")

    def parallel_detect_images(self, image_paths: List[str]) -> List[Dict[str, Any]]:
        """并行检测图片"""
        if not image_paths:
            return []

        # 将图片分配给不同的GPU
        chunks = [image_paths[i::len(self.detectors)] for i in range(len(self.detectors))]

        print(f"🔄 使用 {len(self.detectors)} 个GPU并行处理 {len(image_paths)} 张图片")

        results = []
        with ThreadPoolExecutor(max_workers=len(self.detectors)) as executor:
            futures = []
            for i, (detector, chunk) in enumerate(zip(self.detectors, chunks)):
                if chunk:  # 只处理非空的chunk
                    image_detector = ImageDetector(
                        self.model_path, device=detector.device, **self.kwargs
                    )
                    future = executor.submit(image_detector.detect_batch_images, chunk)
                    futures.append(future)

            for future in as_completed(futures):
                try:
                    batch_results = future.result()
                    results.extend(batch_results)
                except Exception as e:
                    print(f"❌ GPU处理错误: {e}")

        print(f"✅ 多GPU并行处理完成，处理了 {len(results)} 张图片")
        return results

def send_report_to_api(report_data):
    """
    将报告数据通过POST请求发送到API端点。
    在后台线程中执行，以避免阻塞主处理流程。
    """
    def task():
        if API_ENDPOINT == "https://your.api/endpoint/here":
            return

        try:
            headers = {'Content-Type': 'application/json'}
            response = requests.post(API_ENDPOINT, headers=headers, json=report_data, timeout=10)
            if 200 <= response.status_code < 300:
                print(f"✅ 报告成功发送到 API, 状态码: {response.status_code}")
            else:
                print(f"⚠️ API 上报警告: 状态码 {response.status_code}, 响应: {response.text[:200]}")
        except requests.exceptions.RequestException as e:
            print(f"❌ API 上报错误: {e}")

    threading.Thread(target=task, daemon=True).start()

# --- 主要功能函数 ---
def export_tensorrt_model(model_path: str, output_path: str = None, imgsz: int = DEFAULT_IMGSZ,
                         device: str = '0', force: bool = False) -> bool:
    """
    导出TensorRT模型

    Args:
        model_path: 原始模型路径
        output_path: 输出路径 (可选)
        imgsz: 图片大小
        device: 设备
        force: 是否强制重新导出

    Returns:
        bool: 是否成功
    """
    if output_path is None:
        output_path = str(Path(model_path).with_suffix('.engine'))

    if os.path.exists(output_path) and not force:
        print(f"✅ TensorRT模型已存在: {output_path}")
        return True

    try:
        print(f"🔄 正在导出TensorRT模型...")
        model = YOLO(model_path)
        model.export(
            format='engine',
            imgsz=imgsz,
            device=device,
            half=True,
            dynamic=False,
            simplify=True,
            workspace=4
        )
        print(f"✅ TensorRT模型导出成功: {output_path}")
        return True
    except Exception as e:
        print(f"❌ TensorRT导出失败: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='YOLO 多模式智能检测系统 v3.0')

    # 基础参数
    parser.add_argument('--model', type=str, default='runs/detect_100_640/pig_detection/weights/best.pt',
                       help='模型路径')
    parser.add_argument('--mode', type=str, choices=['image', 'video', 'stream'], required=True,
                       help='处理模式: image(图片), video(视频文件), stream(实时流)')
    parser.add_argument('--source', type=str, required=True,
                       help='输入源: 图片路径/目录, 视频文件路径, 或流URL')

    # 检测参数
    parser.add_argument('--conf', type=float, default=DEFAULT_CONF_THRESHOLD, help='置信度阈值')
    parser.add_argument('--iou', type=float, default=DEFAULT_IOU_THRESHOLD, help='NMS IOU阈值')
    parser.add_argument('--imgsz', type=int, default=DEFAULT_IMGSZ, help='推理图片大小')
    parser.add_argument('--device', type=str, default='', help='设备 (cpu, 0, 0,1, ...)')
    parser.add_argument('--max-det', type=int, default=300, help='最大检测数量')

    # TensorRT参数
    parser.add_argument('--tensorrt', action='store_true', help='使用TensorRT加速')
    parser.add_argument('--export-tensorrt', action='store_true', help='导出TensorRT模型')
    parser.add_argument('--force-export', action='store_true', help='强制重新导出TensorRT模型')

    # 多GPU参数
    parser.add_argument('--multi-gpu', action='store_true', help='使用多GPU并行处理')

    # 输出参数
    parser.add_argument('--output-dir', type=str, default='output', help='输出目录')
    parser.add_argument('--save-result', action='store_true', default=True, help='保存结果')
    parser.add_argument('--show-result', action='store_true', help='显示结果')

    # 视频/流特定参数
    parser.add_argument('--target-size', type=int, default=DEFAULT_TARGET_SIZE, help='目标分辨率')
    parser.add_argument('--enable-tracking', action='store_true', default=True, help='启用目标追踪')

    # 流媒体参数
    parser.add_argument('--port', type=int, default=DEFAULT_STREAM_PORT, help='HLS流媒体服务端口')
    parser.add_argument('--public-url', type=str, default='', help='公网访问URL')
    parser.add_argument('--api-report', action='store_true', default=True, help='启用API报告')
    parser.add_argument('--report-interval', type=int, default=15, help='报告间隔(秒)')

    # 性能测试参数
    parser.add_argument('--benchmark', action='store_true', help='运行性能基准测试')
    parser.add_argument('--benchmark-iterations', type=int, default=100, help='基准测试迭代次数')

    args = parser.parse_args()

    # 检查模型文件
    if not os.path.exists(args.model):
        print(f"❌ 错误: 模型文件不存在 {args.model}")
        return

    # 仅导出TensorRT模型
    if args.export_tensorrt:
        success = export_tensorrt_model(args.model, force=args.force_export,
                                      imgsz=args.imgsz, device=args.device or '0')
        return

    print(f"🚀 启动 YOLO 多模式检测系统 v3.0")
    print(f"📋 模式: {args.mode}")
    print(f"🧠 模型: {args.model}")
    print(f"📁 输入源: {args.source}")

    try:
        if args.mode == 'image':
            run_image_detection(args)
        elif args.mode == 'video':
            run_video_detection(args)
        elif args.mode == 'stream':
            run_stream_detection(args)
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"❌ 运行错误: {e}")
        import traceback
        traceback.print_exc()

def run_image_detection(args):
    """运行图片检测"""
    print("\n🖼️ === 图片检测模式 ===")

    # 检查多GPU
    if args.multi_gpu:
        base_detector = BaseDetector(args.model, device='', use_tensorrt=args.tensorrt)
        gpu_devices = base_detector.get_multi_gpu_devices()

        if len(gpu_devices) > 1:
            print(f"🔥 使用多GPU并行处理: {gpu_devices}")
            multi_detector = MultiGPUDetector(
                args.model, gpu_devices, use_tensorrt=args.tensorrt,
                conf_threshold=args.conf, iou_threshold=args.iou, imgsz=args.imgsz
            )

            # 获取图片列表
            if os.path.isdir(args.source):
                image_paths = []
                for ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']:
                    image_paths.extend(glob.glob(os.path.join(args.source, f"*{ext}")))
                    image_paths.extend(glob.glob(os.path.join(args.source, f"*{ext.upper()}")))
            else:
                image_paths = [args.source]

            results = multi_detector.parallel_detect_images(image_paths)
            print(f"✅ 多GPU处理完成，共处理 {len(results)} 张图片")
            return

    # 单GPU处理
    detector = ImageDetector(
        args.model, output_dir=args.output_dir, device=args.device,
        use_tensorrt=args.tensorrt, conf_threshold=args.conf,
        iou_threshold=args.iou, imgsz=args.imgsz
    )

    # 性能测试
    if args.benchmark:
        detector.benchmark_performance(iterations=args.benchmark_iterations)

    # 检测处理
    if os.path.isdir(args.source):
        results = detector.detect_directory(args.source)
    elif os.path.isfile(args.source):
        results = [detector.detect_single_image(args.source, args.save_result, args.show_result)]
    else:
        print(f"❌ 输入源不存在: {args.source}")
        return

    # 输出统计
    successful_results = [r for r in results if 'error' not in r]
    total_detections = sum(r.get('detection_count', 0) for r in successful_results)
    avg_time = np.mean([r.get('inference_time', 0) for r in successful_results]) if successful_results else 0

    print(f"\n📊 检测统计:")
    print(f"   处理图片: {len(successful_results)}/{len(results)}")
    print(f"   总检测数: {total_detections}")
    print(f"   平均推理时间: {avg_time*1000:.2f}ms")

def run_video_detection(args):
    """运行视频检测"""
    print("\n🎬 === 视频检测模式 ===")

    detector = VideoDetector(
        args.model, output_dir=args.output_dir, device=args.device,
        use_tensorrt=args.tensorrt, conf_threshold=args.conf,
        iou_threshold=args.iou, imgsz=args.imgsz
    )

    # 性能测试
    if args.benchmark:
        detector.benchmark_performance(iterations=args.benchmark_iterations)

    # 检测视频
    result = detector.detect_video(
        args.source, save_result=args.save_result, show_result=args.show_result,
        target_size=args.target_size, enable_tracking=args.enable_tracking,
        max_det=args.max_det
    )

    print(f"✅ 视频检测完成: {result.get('output_path', '未保存')}")

def run_stream_detection(args):
    """运行实时流检测"""
    print("\n📡 === 实时流检测模式 ===")

    detector = StreamDetector(
        args.model, stream_port=args.port, device=args.device,
        use_tensorrt=args.tensorrt, conf_threshold=args.conf,
        iou_threshold=args.iou, imgsz=args.imgsz
    )

    # 性能测试
    if args.benchmark:
        detector.benchmark_performance(iterations=args.benchmark_iterations)

    # 开始流处理
    detector.start_streaming(
        args.source, target_size=args.target_size, max_det=args.max_det,
        public_url=args.public_url, enable_api_report=args.api_report,
        report_interval=args.report_interval
    )

if __name__ == "__main__":
    main()
    
    # 快照文件夹和文件名优化
    snapshots_dir = "snapshots"
    os.makedirs(snapshots_dir, exist_ok=True)
    snapshot_path = os.path.join(snapshots_dir, "latest_snapshot.jpg")
    print(f"ℹ️ 快照图片将保存并覆盖于: '{snapshot_path}'")
    
    def run_hls_server():
        try:
            from waitress import serve
            serve(streamer.app, host='0.0.0.0', port=streamer.stream_port)
        except ImportError:
            streamer.app.run(host='0.0.0.0', port=streamer.stream_port)
        
    hls_thread = threading.Thread(target=run_hls_server, daemon=True)
    hls_thread.start()
    
    # 2. 正确处理和打印 URL
    hls_stream_url = ""
    player_page_url = ""
    print("\n" + "="*60)
    print("🚀 HLS 流媒体服务已启动 🚀")
    if public_url:
        # 移除末尾的斜杠，以保证拼接URL的正确性
        base_url = public_url.rstrip('/')
        hls_stream_url = f"{base_url}/live.m3u8"
        player_page_url = f"{base_url}/"
        print(f"✅ 在VLC或支持HLS的播放器中打开此URL: {hls_stream_url}")
        print(f"🌐 或在浏览器中访问播放页面: {player_page_url}")
    else:
        print("⚠️ 警告: 未通过 --public-url 提供公网URL。上报的URL将为空。")
        print(f"   请使用服务商提供的公网地址访问，内部服务运行在 0.0.0.0:{stream_port}")
    print("="*60 + "\n")
    
    cap = cv2.VideoCapture(source)
    if not cap.isOpened():
        print(f"❌ 错误: 无法打开视频源 {source}"); return

    original_fps = cap.get(cv2.CAP_PROP_FPS) or 25
    original_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    original_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    print(f"ℹ️ 源信息: {original_width}x{original_height} @ {original_fps:.2f} FPS")
    scaled_width, scaled_height = calculate_scaled_resolution(original_width, original_height, target_size=target_size)
    print(f"✅ 输出流将缩放至: {scaled_width}x{scaled_height}")

    ffmpeg_cmd = [
        'ffmpeg', '-loglevel', 'error', '-y', '-f', 'rawvideo', '-vcodec', 'rawvideo', '-pix_fmt', 'bgr24',
        '-s', f'{scaled_width}x{scaled_height}',
        '-r', str(original_fps), '-i', '-', '-c:v', 'libx264', '-preset', 'ultrafast',
        '-tune', 'zerolatency', '-g', str(int(original_fps * 2)), '-sc_threshold', '0',
        '-f', 'hls', '-hls_time', '2', '-hls_list_size', '5',
        '-hls_flags', 'delete_segments+program_date_time',
        '-hls_segment_filename', os.path.join(streamer.hls_dir, 'live%03d.ts'),
        os.path.join(streamer.hls_dir, 'live.m3u8'),
    ]
    ffmpeg_process = subprocess.Popen(ffmpeg_cmd, stdin=subprocess.PIPE)

    # 功能模块变量初始化
    min_consecutive_frames = 1 * int(original_fps)  # 稳定检测所需的连续帧数 (例如1秒)
    # 新增：用于跟踪每头猪的连续帧数计数器 {track_id: frame_count}
    pig_track_counters = {}
    
    reporting_interval = 15  # 报告周期（秒）
    last_report_time = time.time()
    interval_max_pigs = 0
    interval_best_frame_time = None
    interval_best_frame = None

    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                print("⚠️ 视频流中断，尝试重连..."); cap.release(); time.sleep(2)
                cap = cv2.VideoCapture(source)
                if not cap.isOpened(): print("❌ 重连失败，退出。"); break
                continue
            
            resized_frame = cv2.resize(frame, (scaled_width, scaled_height), interpolation=cv2.INTER_AREA)
            
            # 修改：使用 model.track() 来获取带ID的检测结果
            results = model.track(resized_frame, persist=True, conf=conf_threshold, iou=iou_threshold, max_det=max_det, imgsz=imgsz, verbose=False)
            
            stable_pig_count = 0
            current_track_ids = set()

            # 检查是否有跟踪结果并且结果中包含ID
            if results[0].boxes is not None and results[0].boxes.id is not None:
                # 1. 获取当前帧所有被跟踪到的猪的ID
                current_track_ids = set(results[0].boxes.id.cpu().numpy().astype(int))

                # 2. 更新或初始化这些ID的计数器
                for track_id in current_track_ids:
                    # 如果ID已在跟踪，计数值+1；如果是新ID，则从0+1=1开始计数
                    pig_track_counters[track_id] = pig_track_counters.get(track_id, 0) + 1
            
            # 3. 清理已消失的猪的计数器
            # 找出那些之前在跟踪但当前帧已消失的ID
            disappeared_ids = set(pig_track_counters.keys()) - current_track_ids
            for track_id in disappeared_ids:
                del pig_track_counters[track_id] # 从字典中移除，实现计数清零

            # 4. 计算当前帧的“稳定”猪只数量
            # 遍历所有仍在跟踪的猪，如果其连续出现帧数达标，则计为稳定猪
            if pig_track_counters:
                for track_id in pig_track_counters:
                    if pig_track_counters[track_id] >= min_consecutive_frames:
                        stable_pig_count += 1

            if stable_pig_count > interval_max_pigs:
                interval_max_pigs = stable_pig_count
                interval_best_frame_time = datetime.now()
                # 仅在找到更优结果时才复制和保存帧，优化性能
                interval_best_frame = resized_frame.copy() 
                # 我们只绘制检测框，不绘制跟踪ID和置信度，以保持画面简洁
                annotated_best_frame = draw_boxes_only(interval_best_frame, results[0].boxes)
                cv2.imwrite(snapshot_path, annotated_best_frame)

            annotated_frame = draw_boxes_only(resized_frame, results[0].boxes)
            try:
                ffmpeg_process.stdin.write(annotated_frame.tobytes())
            except (BrokenPipeError, OSError):
                print("❌ FFmpeg 进程关闭，停止推流。"); break

            current_time = time.time()
            if current_time - last_report_time >= reporting_interval:
                base64_picture = None
                # 注意：这里我们上报的是快照文件，而不是内存中的frame
                if os.path.exists(snapshot_path):
                    try:
                        with open(snapshot_path, "rb") as image_file:
                            base64_picture = base64.b64encode(image_file.read()).decode('utf-8')
                    except Exception as e:
                        print(f"❌ 图片读取或Base64编码失败: {e}")

                report_data = {
                    "pig_amount": interval_max_pigs,
                    "detect_time": interval_best_frame_time.strftime('%Y-%m-%d %H:%M:%S') if interval_best_frame_time else datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    "url": hls_stream_url,
                    "picture": base64_picture
                }
                
                log_data = report_data.copy()
                if log_data.get('picture'):
                    log_data['picture'] = f"Base64 string (size: {len(log_data['picture'])} bytes)"
                
                print("\n--- 15秒周期报告 (准备上报) ---")
                print(f"有效猪只数量: {report_data['pig_amount']}")
                print(f"最佳检测时间: {report_data['detect_time']}")
                # print(json.dumps(log_data, ensure_ascii=False, indent=4))
                print("--------------------------------\n")

                send_report_to_api(report_data)
                
                # 重置周期统计变量
                last_report_time = current_time
                interval_max_pigs = 0
                interval_best_frame_time = None
                interval_best_frame = None
                # 清理快照，避免旧图片被误用
                if os.path.exists(snapshot_path):
                    try:
                        os.remove(snapshot_path)
                    except OSError as e:
                        print(f"⚠️ 清理快照失败: {e}")

    except KeyboardInterrupt:
        print("\nℹ️ 收到中断信号，正在停止服务...")
    finally:
        cap.release()
        if ffmpeg_process and ffmpeg_process.stdin:
            try: ffmpeg_process.stdin.close()
            except (BrokenPipeError, OSError): pass
        if ffmpeg_process: ffmpeg_process.wait()
        print("✅ 服务已安全停止。")


def main():
    parser = argparse.ArgumentParser(description='猪检测智能推流脚本')
    parser.add_argument('--model', type=str, default='runs/detect_100_640/pig_detection/weights/best.pt', help='模型路径')
    parser.add_argument('--source', type=str, required=True, help='输入视频流 (例如 "rtmp://..." 或 "0")')
    parser.add_argument('--conf', type=float, default=0.25, help='置信度阈值')
    parser.add_argument('--iou', type=float, default=0.45, help='NMS IOU阈值')
    parser.add_argument('--target_size', type=int, default=1280, help='目标分辨率')
    parser.add_argument('--imgsz', type=int, default=640, help='推理图片大小')
    parser.add_argument('--port', type=int, default=6006, help='HLS流媒体服务端口')
    parser.add_argument('--device', type=str, default='', help='推理设备 (cpu, 0, ...)')
    parser.add_argument('--public-url', type=str, default='https://u8076-b250-ef46accb.bjb1.seetacloud.com:8443', help='AutoDL 提供的公网访问 URL')
    args = parser.parse_args()

    if not os.path.exists(args.model):
        print(f"❌ 错误: 模型文件 {args.model} 不存在!"); return
        
    if API_ENDPOINT == "https://your.api/endpoint/here":
        print("⚠️ 警告: API端点未配置。周期报告将仅在本地打印，不会发送到远程服务器。")
        print("   请修改脚本中的 `API_ENDPOINT` 变量以启用上报功能。")

    print("🧠 正在加载模型...")
    model = YOLO(args.model)
    if args.device: model.to(args.device)
    
    start_streaming(model, args.source, args.conf, args.iou, target_size=args.target_size,
                   max_det=300, stream_port=args.port, imgsz=args.imgsz, public_url=args.public_url)

if __name__ == "__main__":
    main()
