#!/usr/bin/env python3
"""
测试新的YOLO多模式检测系统
"""

import os
import sys
import tempfile
import numpy as np
import cv2
from pathlib import Path

# 添加当前目录到路径
sys.path.insert(0, '.')

def create_test_image(width=640, height=640, filename="test_image.jpg"):
    """创建测试图片"""
    # 创建随机图片
    image = np.random.randint(0, 255, (height, width, 3), dtype=np.uint8)
    
    # 添加一些简单的形状作为"目标"
    cv2.rectangle(image, (100, 100), (200, 200), (255, 0, 0), -1)
    cv2.circle(image, (400, 400), 50, (0, 255, 0), -1)
    
    cv2.imwrite(filename, image)
    return filename

def test_basic_import():
    """测试基本导入"""
    print("🔄 测试基本导入...")
    try:
        from testpredict_tracker_new import BaseDetector, ImageDetector, VideoDetector, StreamDetector
        print("✅ 基本导入成功")
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_base_detector():
    """测试基础检测器"""
    print("🔄 测试基础检测器...")
    try:
        from testpredict_tracker_new import BaseDetector
        
        # 检查模型文件是否存在
        model_path = "runs/detect_100_640/pig_detection/weights/best.pt"
        if not os.path.exists(model_path):
            print(f"⚠️ 模型文件不存在: {model_path}")
            return False
        
        # 创建检测器
        detector = BaseDetector(model_path, device='cpu')
        print(f"✅ 基础检测器创建成功")
        print(f"   可用GPU: {detector.available_gpus}")
        print(f"   模型路径: {detector.model_path}")
        
        return True
    except Exception as e:
        print(f"❌ 基础检测器测试失败: {e}")
        return False

def test_image_detector():
    """测试图片检测器"""
    print("🔄 测试图片检测器...")
    try:
        from testpredict_tracker_new import ImageDetector
        
        model_path = "runs/detect_100_640/pig_detection/weights/best.pt"
        if not os.path.exists(model_path):
            print(f"⚠️ 模型文件不存在: {model_path}")
            return False
        
        # 创建测试图片
        test_image = create_test_image()
        
        # 创建检测器
        detector = ImageDetector(model_path, device='cpu', output_dir="test_output")
        
        # 执行检测
        result = detector.detect_single_image(test_image, save_result=True, show_result=False)
        
        print(f"✅ 图片检测成功")
        print(f"   检测数量: {result['detection_count']}")
        print(f"   推理时间: {result['inference_time']*1000:.2f}ms")
        print(f"   输出路径: {result['output_path']}")
        
        # 清理
        os.remove(test_image)
        
        return True
    except Exception as e:
        print(f"❌ 图片检测器测试失败: {e}")
        return False

def test_performance_benchmark():
    """测试性能基准"""
    print("🔄 测试性能基准...")
    try:
        from testpredict_tracker_new import BaseDetector
        
        model_path = "runs/detect_100_640/pig_detection/weights/best.pt"
        if not os.path.exists(model_path):
            print(f"⚠️ 模型文件不存在: {model_path}")
            return False
        
        detector = BaseDetector(model_path, device='cpu')
        
        # 运行性能测试
        results = detector.benchmark_performance(iterations=10)
        
        print(f"✅ 性能基准测试成功")
        print(f"   平均推理时间: {results['avg_inference_time']*1000:.2f}ms")
        print(f"   理论FPS: {results['fps']:.2f}")
        
        return True
    except Exception as e:
        print(f"❌ 性能基准测试失败: {e}")
        return False

def test_multi_gpu_detection():
    """测试多GPU检测"""
    print("🔄 测试多GPU检测...")
    try:
        from testpredict_tracker_new import BaseDetector, MultiGPUDetector
        
        model_path = "runs/detect_100_640/pig_detection/weights/best.pt"
        if not os.path.exists(model_path):
            print(f"⚠️ 模型文件不存在: {model_path}")
            return False
        
        # 检查GPU
        base_detector = BaseDetector(model_path, device='cpu')
        gpu_devices = base_detector.get_multi_gpu_devices()
        
        print(f"✅ 多GPU检测器测试")
        print(f"   可用设备: {gpu_devices}")
        
        if len(gpu_devices) > 1:
            print("   检测到多GPU，可以进行并行处理")
        else:
            print("   只有单个设备，多GPU功能在单GPU环境下正常")
        
        return True
    except Exception as e:
        print(f"❌ 多GPU检测器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试新的YOLO多模式检测系统")
    print("="*60)
    
    tests = [
        ("基本导入", test_basic_import),
        ("基础检测器", test_base_detector),
        ("图片检测器", test_image_detector),
        ("性能基准", test_performance_benchmark),
        ("多GPU检测", test_multi_gpu_detection),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 40)
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "="*60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！新系统运行正常")
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
