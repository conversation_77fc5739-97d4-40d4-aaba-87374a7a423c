# YOLO 多模式智能检测系统 v3.0

## 概述

这是一个基于 Ultralytics YOLO 的多模式智能检测系统，支持图片、视频和实时流的目标检测与追踪。系统经过完全重构，提供了模块化的架构和丰富的功能。

## 主要特性

### 🔥 核心功能
- **三种处理模式**：图片检测、视频检测、实时流检测
- **目标追踪**：视频和实时流支持目标追踪功能
- **TensorRT加速**：支持模型导出和TensorRT推理加速
- **多GPU并行**：自动检测并利用多GPU进行并行处理
- **HLS流媒体**：实时流输出支持，可在浏览器中观看
- **API数据上报**：支持检测结果的API上报功能

### 🚀 性能优化
- **智能分辨率缩放**：自动等比缩放至指定尺寸
- **性能基准测试**：内置性能测试功能
- **稳定性过滤**：过滤短暂出现的检测目标
- **内存优化**：优化的内存使用和资源管理

## 安装要求

### 基础依赖
```bash
pip install ultralytics opencv-python torch torchvision
pip install flask flask-cors waitress requests numpy
```

### TensorRT支持（可选）
```bash
# TensorRT会在首次导出时自动安装
# 或手动安装：pip install tensorrt
```

## 使用方法

### 1. 图片检测

#### 单张图片检测
```bash
python testpredict_tracker_new.py --mode image --source /path/to/image.jpg
```

#### 批量图片检测
```bash
python testpredict_tracker_new.py --mode image --source /path/to/images/
```

#### 多GPU并行处理
```bash
python testpredict_tracker_new.py --mode image --source /path/to/images/ --multi-gpu
```

#### 使用TensorRT加速
```bash
python testpredict_tracker_new.py --mode image --source /path/to/image.jpg --tensorrt
```

### 2. 视频检测

#### 基础视频检测
```bash
python testpredict_tracker_new.py --mode video --source /path/to/video.mp4
```

#### 启用目标追踪
```bash
python testpredict_tracker_new.py --mode video --source /path/to/video.mp4 --enable-tracking
```

#### 显示实时结果
```bash
python testpredict_tracker_new.py --mode video --source /path/to/video.mp4 --show-result
```

### 3. 实时流检测

#### 摄像头检测
```bash
python testpredict_tracker_new.py --mode stream --source 0
```

#### RTMP/RTSP流检测
```bash
python testpredict_tracker_new.py --mode stream --source rtmp://your-stream-url
```

#### 配置公网访问
```bash
python testpredict_tracker_new.py --mode stream --source 0 --public-url http://your-domain.com:6006
```

### 4. TensorRT模型导出

#### 导出TensorRT模型
```bash
python testpredict_tracker_new.py --export-tensorrt --model /path/to/model.pt
```

#### 强制重新导出
```bash
python testpredict_tracker_new.py --export-tensorrt --model /path/to/model.pt --force-export
```

### 5. 性能测试

#### 运行性能基准测试
```bash
python testpredict_tracker_new.py --mode image --source /path/to/image.jpg --benchmark
```

#### 自定义测试迭代次数
```bash
python testpredict_tracker_new.py --mode image --source /path/to/image.jpg --benchmark --benchmark-iterations 200
```

## 参数说明

### 基础参数
- `--model`: 模型路径（默认：runs/detect_100_640/pig_detection/weights/best.pt）
- `--mode`: 处理模式（image/video/stream）
- `--source`: 输入源路径
- `--conf`: 置信度阈值（默认：0.25）
- `--iou`: NMS IOU阈值（默认：0.45）
- `--device`: 设备选择（cpu, 0, 0,1等）

### TensorRT参数
- `--tensorrt`: 使用TensorRT加速
- `--export-tensorrt`: 导出TensorRT模型
- `--force-export`: 强制重新导出

### 多GPU参数
- `--multi-gpu`: 使用多GPU并行处理

### 输出参数
- `--output-dir`: 输出目录（默认：output）
- `--save-result`: 保存结果
- `--show-result`: 显示结果

### 视频/流参数
- `--target-size`: 目标分辨率（默认：1280）
- `--enable-tracking`: 启用目标追踪
- `--max-det`: 最大检测数量（默认：300）

### 流媒体参数
- `--port`: HLS服务端口（默认：6006）
- `--public-url`: 公网访问URL
- `--api-report`: 启用API报告
- `--report-interval`: 报告间隔秒数（默认：15）

### 性能测试参数
- `--benchmark`: 运行性能基准测试
- `--benchmark-iterations`: 测试迭代次数（默认：100）

## 输出说明

### 图片检测输出
- 检测结果图片保存在指定输出目录
- 控制台显示检测统计信息
- 支持批量处理进度显示

### 视频检测输出
- 处理后的视频文件
- 实时进度显示
- 追踪统计信息

### 实时流输出
- HLS流媒体服务（.m3u8格式）
- 浏览器播放页面
- 周期性API数据上报

## API数据格式

系统会定期向配置的API端点发送检测结果：

```json
{
    "pig_amount": 5,
    "detect_time": "2025-01-01 12:00:00",
    "url": "http://your-domain.com:6006/live.m3u8",
    "picture": "base64_encoded_image_data"
}
```

## 性能优化建议

### 1. 硬件优化
- 使用GPU进行推理（--device 0）
- 多GPU环境使用并行处理（--multi-gpu）
- 使用TensorRT加速（--tensorrt）

### 2. 参数调优
- 调整置信度阈值（--conf）
- 优化目标分辨率（--target-size）
- 限制最大检测数量（--max-det）

### 3. 内存优化
- 批量处理时控制并行数
- 定期清理临时文件
- 监控内存使用情况

## 故障排除

### 常见问题

1. **模型文件不存在**
   - 检查模型路径是否正确
   - 确保模型文件完整

2. **CUDA内存不足**
   - 减少batch size
   - 降低图片分辨率
   - 使用CPU推理

3. **TensorRT导出失败**
   - 检查CUDA和TensorRT版本兼容性
   - 确保有足够的磁盘空间

4. **流媒体无法访问**
   - 检查防火墙设置
   - 确认端口未被占用
   - 验证公网URL配置

## 更新日志

### v3.0 (当前版本)
- 完全重构代码架构
- 新增TensorRT加速支持
- 新增多GPU并行处理
- 新增性能基准测试
- 优化内存使用和性能
- 改进错误处理和日志

### v2.1 (原版本)
- 基础的实时流检测
- 目标追踪功能
- API数据上报

## 许可证

本项目基于原有项目进行重构和增强，保持原有的使用许可。
